/**
 * @fileoverview Component for handling user login functionality with form validation and navigation
 * <AUTHOR>
 * @version 1.0.0
 */

import { CommonModule } from '@angular/common';
import {
    Component,
    TemplateRef,
    computed,
    inject,
    signal,
} from '@angular/core';
import {
    FormBuilder,
    FormGroup,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { RouterLink } from '@angular/router';
import {
    ErrorMessage,
    ModalComponent,
    PageFooterComponent,
    TextInputComponent,
} from 'lib-ui-kit';
import { formValidationMessages } from 'lib/lib-app-core/src/lib/constants/form-validation-message';
import { LibAuthLoginBL } from 'lib/lib-auth/src/lib/business-layer/lib-auth-login-bl.service';
import { LibAuthLoginDL } from '../../data-layer/lib-auth-login-dl.service';
import { LibUserDetailDL } from '../../data-layer/lib-auth-user-details-dl.service';
import { LoginFormData } from '../../interfaces/login-form.interface';
import { ForgotPasswordComponent } from '../forgot-password/forgot-password.component';
import { STRONG_PASSWORD_REGEX } from '../registration-form-base/registration-form-base.component';
import { LoginEncryptionService } from '../../services/login-encryption.service';
import { WaiverRoutingServiceBL } from 'projects/online-waiver/src/app/services/business-layer/waiver-routing-bl.service';


export type ErrorMessagLogin<T> = {
    [K in keyof T]?: Record<string, string | TemplateRef<Component> | null>;
};

@Component({
    selector: 'lib-login-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ForgotPasswordComponent,
        TextInputComponent,
        ModalComponent,
        PageFooterComponent,
        RouterLink,
    ],
    templateUrl: './login-form.component.html',
    styleUrl: './login-form.component.css',
    providers: [
        LibAuthLoginBL,
        LibAuthLoginDL,
        LibUserDetailDL,
        LoginEncryptionService,
        WaiverRoutingServiceBL,
    ],
})
export class LoginFormComponent {
    readonly showForgotPassword = signal(false);
    readonly loginForm: FormGroup;
    private readonly _authBL = inject(LibAuthLoginBL);
    readonly loading = computed(() => this._authBL.loading() || this._authBL.routingServiceLoading());
    readonly loginErrorMessage = this._authBL.errorMessage;

    errorMessages: ErrorMessage<LoginFormData> = {
        email: formValidationMessages.email,
        password: formValidationMessages.password,
    };

    constructor(private fb: FormBuilder) {
        this.loginForm = this.fb.group({
            email: ['', [Validators.required, Validators.email]],
            password: [
                '',
                [
                    Validators.required,
                    Validators.pattern(STRONG_PASSWORD_REGEX),
                ],
            ],
        });

        /**
         * Subscribes to the form value changes and resets the login error state
         * on form change
         */
        this.loginForm.valueChanges.subscribe(() => {
            // Reset login error state on form change
            if (this.loginErrorMessage()) {
                this._authBL.errorMessage.set(null);
            }
        });
    }

    /**
     * Handles the form submission for the login process
     *
     * This method validates the form and triggers the login process
     * if the form is valid.
     */
    onSubmit() {
        const { email, password } = this.loginForm.value;
        this._authBL.login(email, password);
    }

    closeForgotPassword() {
        this.showForgotPassword.set(false);
    }
}
