<div class="bg-transparent md:bg-surface-white md:rounded-4xl h-full md:h-[calc(100vh-168px)]">
    <!-- Profile Title (Mobile Only) -->
    <h1 i18n="profile.title" class="text-lg font-semibold text-primary mb-5 md:hidden">My Profile</h1>

    <!-- Profile Card -->
    <div class="h-full bg-surface-white shadow-lg p-6 mb-8 relative rounded-4xl">
        <!-- Title and Edit <PERSON> (Desktop) -->
        <div class="hidden md:flex items-center justify-between mb-6">
            <h1 i18n="profile.title" class="text-lg font-semibold text-primary">My Profile</h1>
            @if (!isEditMode) {
            <button (click)="toggleEditMode()" class="text-secondary-blue hover:text-secondary-blue-dark">
                <img src="assets/icons/edit.svg" alt="Edit" />
            </button>
            }
        </div>

        <!-- <PERSON> (Mobile) -->
        @if (!isEditMode) {
        <button (click)="toggleEditMode()" class="absolute top-6 right-6 md:hidden">
            <img src="assets/icons/edit.svg" alt="Edit" />
        </button>
        }

        <!-- Loading State -->
        @if (loading()) {
        <div class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span class="ml-2 text-neutral-dark">Loading profile...</span>
        </div>
        }

        <!-- Error State -->
        @if (error() && !loading()) {
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <p class="text-red-600">{{ error() }}</p>
            <button (click)="loadUserProfile()" class="mt-2 text-sm text-red-700 underline">
                Try again
            </button>
        </div>
        }

        <!-- View Mode -->
        @if (!isEditMode && !loading() && !error() && profile()) {
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6">
            <div>
                <p i18n="profile.firstName" class="text-xs text-neutral-dark mb-1">First Name</p>
                <p class="text-primary">{{ profile()?.firstName || 'N/A' }}</p>
            </div>

            <div>
                <p i18n="profile.lastName" class="text-xs text-neutral-dark mb-1">Last Name</p>
                <p class="text-primary">{{ profile()?.lastName || 'N/A' }}</p>
            </div>

            <div>
                <p i18n="profile.dateOfBirth" class="text-xs text-neutral-dark mb-1">Date of Birth</p>
                <p class="text-primary">{{ profile()?.dateOfBirth || 'N/A' }}</p>
            </div>

            <div>
                <p i18n="profile.emailAddress" class="text-xs text-neutral-dark mb-1">Email Address</p>
                <p class="text-primary">{{ profile()?.email || 'N/A' }}</p>
            </div>

            <div>
                <p i18n="profile.mobileNumber" class="text-xs text-neutral-dark mb-1">Mobile Number</p>
                <p class="text-primary">
                    {{ profile()?.countryCode || '' }} {{ profile()?.mobileNumber || 'N/A' }}
                </p>
            </div>
        </div>
        }

        <!-- Edit Mode -->
        @if (isEditMode) {
        <form [formGroup]="profileForm">
            <!-- Dynamic Form -->
            <lib-dynamic-form
                [fields]="profileFields()"
                [form]="profileForm"
                [errorMessages]="errorMessages"
                [columns]="3"
            />

            <!-- Action Buttons -->
            <div class="mt-6 pt-4 border-t border-neutral-light">
                <div class="flex flex-col sm:flex-row gap-4 lg:w-[60%]">
                    <button type="button" (click)="updateProfile()" [disabled]="profileForm.invalid || loading()"
                        class="w-full py-3 px-4 bg-primary text-white font-medium rounded-full disabled:bg-surface">
                        {{ loading() ? 'Updating...' : 'Update' }}
                    </button>
                    <button type="button" (click)="cancelEdit()" [disabled]="loading()"
                        class="w-full py-3 px-4 bg-surface-white text-black border border-primary font-medium rounded-full disabled:opacity-50">
                        Cancel
                    </button>
                </div>
            </div>
        </form>
        }
    </div>
</div>