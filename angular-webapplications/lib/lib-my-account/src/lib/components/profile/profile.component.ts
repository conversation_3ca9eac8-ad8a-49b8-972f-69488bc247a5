import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DATE_FORMAT, DatePickerComponent, ErrorMessage, PhoneInputComponent, TextInputComponent } from 'lib-ui-kit';
import { CustomerRelationBL } from 'projects/online-waiver/src/app/services/business-layer/customer-relation-bl.service';
import { CookieService } from 'lib-app-core';
import { UserLoginDTOModel } from 'lib/lib-auth/src/lib/models/user-login-dto.model';
import { switchMap, take } from 'rxjs/operators';
interface ProfileFormData {
    firstName: FormControl<string | null>;
    lastName: FormControl<string | null>;
    dateOfBirth: FormControl<string | null>;
    email: FormControl<string | null>;
    mobileNumber: FormControl<string | null>;
}

@Component({
    selector: 'lib-profile',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        TextInputComponent,
        DatePickerComponent,
        PhoneInputComponent,
    ],
    providers: [
        CustomerRelationBL
    ],
    templateUrl: './profile.component.html',
    styleUrl: './profile.component.css'
})
export class ProfileComponent implements OnInit {
    protected readonly dateFormat = DATE_FORMAT.YYYY_MM_DD;

    // Inject services
    private _customerRelationBL = inject(CustomerRelationBL);
    private _cookieService = inject(CookieService);

    // Profile data signal
    profile = signal<{
        firstName: string;
        lastName: string;
        dateOfBirth: string;
        email: string;
        mobileNumber: string;
        countryCode: string;
    } | null>(null);

    isEditMode = false;
    profileForm: FormGroup;
    loading = signal(false);
    error = signal<string | null>(null);

    // Error messages for form validation
    errorMessages: ErrorMessage<ProfileFormData> = {
        email: {
            required: 'Email is required',
            email: 'Please enter a valid email address',
        },
        mobileNumber: {
            required: 'Mobile number is required',
        },
        firstName: {
            required: 'First name is required',
        },
        lastName: {
            required: 'Last name is required',
        },
        dateOfBirth: {
            required: 'Date of birth is required',
        },
    };

    constructor(private fb: FormBuilder) {
        // Initialize form with empty values - will be populated in ngOnInit
        this.profileForm = this.fb.group({
            firstName: ['', Validators.required],
            lastName: ['', Validators.required],
            dateOfBirth: ['', Validators.required],
            email: ['', [Validators.required, Validators.email]],
            mobileNumber: ['', Validators.required],
        });
    }

    ngOnInit(): void {
        this.loadUserProfile();
    }

    /**
     * Load user profile data from the API
     */
    loadUserProfile(): void {
        this.loading.set(true);
        this.error.set(null);

        // Get userId from cookies
        const userId = this._cookieService.getCookie('userId');
        if (!userId) {
            this.error.set('User not authenticated');
            this.loading.set(false);
            return;
        }

        // Use the existing CustomerRelationBL service to get primary customer data
        this._customerRelationBL.getPrimaryCustomerData()
            .pipe(take(1))
            .subscribe({
                next: (response) => {
                    if (response.loading) {
                        return; // Still loading
                    }

                    if (response.error) {
                        this.error.set(response.error);
                        this.loading.set(false);
                        return;
                    }

                    if (response.data) {
                        this.populateProfileData(response.data);
                    } else {
                        this.error.set('No user data found');
                    }
                    this.loading.set(false);
                },
                error: (err) => {
                    this.error.set('Failed to load user profile');
                    this.loading.set(false);
                    console.error('Error loading user profile:', err);
                }
            });
    }

    /**
     * Transform API data to profile format and populate form
     */
    private populateProfileData(participant: any): void {
        // Extract phone number and country code
        const phoneNumber = participant.phoneNumber || '';
        const email = participant.email || '';

        const profileData = {
            firstName: participant.firstName || '',
            lastName: participant.lastName || '',
            dateOfBirth: participant.dateOfBirth || '',
            email: email,
            mobileNumber: phoneNumber,
            countryCode: '+1', // Default country code - you might want to extract this from the API
        };

        this.profile.set(profileData);

        // Update form with the loaded data
        this.profileForm.patchValue({
            firstName: profileData.firstName,
            lastName: profileData.lastName,
            dateOfBirth: profileData.dateOfBirth,
            email: profileData.email,
            mobileNumber: profileData.mobileNumber,
        });
    }

    toggleEditMode(): void {
        this.isEditMode = !this.isEditMode;

        if (this.isEditMode) {
            // Reset form with current profile values when entering edit mode
            const currentProfile = this.profile();
            if (currentProfile) {
                this.profileForm.patchValue({
                    firstName: currentProfile.firstName,
                    lastName: currentProfile.lastName,
                    dateOfBirth: currentProfile.dateOfBirth,
                    email: currentProfile.email,
                    mobileNumber: currentProfile.mobileNumber,
                });
            }
        }
    }

    updateProfile(): void {
        if (this.profileForm.valid) {
            // Update profile with form values
            const formValues = this.profileForm.value;
            const currentProfile = this.profile();

            if (currentProfile) {
                const updatedProfile = {
                    ...currentProfile,
                    ...formValues,
                };
                this.profile.set(updatedProfile);
            }

            // Exit edit mode
            this.isEditMode = false;

            // In a real app, you would save to backend here
            console.log('Profile updated:', this.profile());
        } else {
            // Mark all fields as touched to show validation errors
            this.profileForm.markAllAsTouched();
        }
    }

    cancelEdit(): void {
        // Exit edit mode without saving changes
        this.isEditMode = false;
    }
}
