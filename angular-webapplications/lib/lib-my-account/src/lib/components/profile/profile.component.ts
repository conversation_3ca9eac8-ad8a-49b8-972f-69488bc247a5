import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ErrorMessage, DynamicFormField } from 'lib-ui-kit';
import { CookieService } from 'lib-app-core';
import { DynamicFormComponent } from 'projects/online-waiver/src/app/components/dynamic-form/dynamic-form.component';
import { PrimaryCustomerDL } from 'projects/online-waiver/src/app/services/data-layer/primary-customer-dl.service';
interface ProfileFormData {
    firstName: FormControl<string | null>;
    lastName: FormControl<string | null>;
    dateOfBirth: FormControl<string | null>;
    email: FormControl<string | null>;
    mobileNumber: FormControl<string | null>;
}

@Component({
    selector: 'lib-profile',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        DynamicFormComponent,
    ],
    providers: [
        PrimaryCustomerDL
    ],
    templateUrl: './profile.component.html',
    styleUrl: './profile.component.css'
})
export class ProfileComponent implements OnInit {
    // Inject services
    private _primaryCustomerDL = inject(PrimaryCustomerDL);
    private _cookieService = inject(CookieService);

    // Profile data signal
    profile = signal<{
        firstName: string;
        lastName: string;
        dateOfBirth: string;
        email: string;
        mobileNumber: string;
        countryCode: string;
    } | null>(null);

    isEditMode = false;
    profileForm: FormGroup;
    loading = signal(false);
    error = signal<string | null>(null);

    // Dynamic form fields
    profileFields = signal<DynamicFormField[]>([
        {
            id: 'firstName',
            fieldName: 'firstName',
            label: 'First Name',
            type: 'text',
            required: true,
            placeholder: 'Enter your first name'
        },
        {
            id: 'lastName',
            fieldName: 'lastName',
            label: 'Last Name',
            type: 'text',
            required: true,
            placeholder: 'Enter your last name'
        },
        {
            id: 'dateOfBirth',
            fieldName: 'dateOfBirth',
            label: 'Date of Birth',
            type: 'date',
            required: true,
            placeholder: 'Select your date of birth',
            maxYear: new Date().getFullYear() - 13, // Minimum age 13
            minYear: new Date().getFullYear() - 100 // Maximum age 100
        },
        {
            id: 'email',
            fieldName: 'email',
            label: 'Email Address',
            type: 'email',
            required: true,
            placeholder: 'Enter your email address'
        },
        {
            id: 'mobileNumber',
            fieldName: 'mobileNumber',
            label: 'Mobile Number',
            type: 'phone',
            required: true,
            placeholder: 'Enter your mobile number',
            countryCodeOptions: [
                { name: 'United States', dialCode: '+1' },
                { name: 'Canada', dialCode: '+1' },
                { name: 'United Kingdom', dialCode: '+44' }
            ]
        }
    ]);

    // Error messages for form validation
    errorMessages: ErrorMessage<ProfileFormData> = {
        email: {
            required: 'Email is required',
            email: 'Please enter a valid email address',
        },
        mobileNumber: {
            required: 'Mobile number is required',
        },
        firstName: {
            required: 'First name is required',
        },
        lastName: {
            required: 'Last name is required',
        },
        dateOfBirth: {
            required: 'Date of birth is required',
        },
    };

    constructor(private fb: FormBuilder) {
        // Initialize form from dynamic fields
        this.profileForm = this.createFormFromFields();
    }

    /**
     * Create form group from dynamic fields
     */
    private createFormFromFields(): FormGroup {
        const group: Record<string, FormControl> = {};

        this.profileFields().forEach(field => {
            const validators = [];
            if (field.required) {
                validators.push(Validators.required);
            }
            if (field.type === 'email') {
                validators.push(Validators.email);
            }

            group[field.fieldName] = new FormControl('', validators);
        });

        return this.fb.group(group);
    }

    ngOnInit(): void {
        this.loadUserProfile();
    }



    /**
     * Load user profile data from the API
     */
    loadUserProfile(): void {
        // Get userId from cookies
        const userId = this._cookieService.getCookie('userId');
        if (!userId) {
            this.error.set('User not authenticated');
            return;
        }

        this.loading.set(true);
        this.error.set(null);

        // Use PrimaryCustomerDL directly to avoid unnecessary API calls
        this._primaryCustomerDL.buildApiParams({ customerId: userId });
        this._primaryCustomerDL.load().subscribe({
            next: (response) => {
                console.log('Primary customer response:', response);
                this.loading.set(false);

                if (response.data && response.data.length > 0) {
                    const customerData = response.data[0];
                    this.populateProfileData(customerData);
                } else {
                    this.error.set('No user data found');
                }
            },
            error: (err) => {
                console.error('Error loading user profile:', err);
                this.error.set('Failed to load user profile');
                this.loading.set(false);
            }
        });
    }



    /**
     * Transform API data to profile format and populate form
     */
    private populateProfileData(customerData: any): void {
        console.log('Populating profile with data:', customerData);

        // Extract data from UserLoginDTOModel
        const profileData = {
            firstName: customerData.FirstName || '',
            lastName: customerData.LastName || '',
            dateOfBirth: customerData.DateOfBirth || '',
            email: customerData.Email || '',
            mobileNumber: customerData.PhoneNumber || '',
            countryCode: '+1', // Default country code - you might want to extract this from the API
        };

        this.profile.set(profileData);

        // Update form with the loaded data
        this.profileForm.patchValue({
            firstName: profileData.firstName,
            lastName: profileData.lastName,
            dateOfBirth: profileData.dateOfBirth,
            email: profileData.email,
            mobileNumber: profileData.mobileNumber,
        });
    }

    toggleEditMode(): void {
        this.isEditMode = !this.isEditMode;

        if (this.isEditMode) {
            // Reset form with current profile values when entering edit mode
            const currentProfile = this.profile();
            if (currentProfile) {
                this.profileForm.patchValue({
                    firstName: currentProfile.firstName,
                    lastName: currentProfile.lastName,
                    dateOfBirth: currentProfile.dateOfBirth,
                    email: currentProfile.email,
                    mobileNumber: currentProfile.mobileNumber,
                });
            }
        }
    }

    updateProfile(): void {
        if (this.profileForm.valid) {
            // Update profile with form values
            const formValues = this.profileForm.value;
            const currentProfile = this.profile();

            if (currentProfile) {
                const updatedProfile = {
                    ...currentProfile,
                    ...formValues,
                };
                this.profile.set(updatedProfile);
            }

            // Exit edit mode
            this.isEditMode = false;

            // In a real app, you would save to backend here
            console.log('Profile updated:', this.profile());
        } else {
            // Mark all fields as touched to show validation errors
            this.profileForm.markAllAsTouched();
        }
    }

    cancelEdit(): void {
        // Exit edit mode without saving changes
        this.isEditMode = false;
    }
}
