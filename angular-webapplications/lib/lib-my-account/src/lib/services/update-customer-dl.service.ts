/**
 * @fileoverview Data layer service for updating customer profile
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable } from 'rxjs';

export interface UpdateCustomerParams {
    customerId: string;
}

export interface UpdateCustomerPayload {
    FirstName: string;
    LastName: string;
    DateOfBirth: string;
    Email: string;
    PhoneNumber: string;
    // Add other fields as needed
}

@Injectable({
    providedIn: 'root'
})
export class UpdateCustomerDL extends ApiServiceBase {
    private _apiParams!: UpdateCustomerParams;
    private _apiPayload!: UpdateCustomerPayload;

    constructor() {
        super('update_customer_data', 'PASSWORD_RESET'); // Reusing PASSWORD_RESET endpoint which is /Customer/Customers
        this.init();
    }

    buildApiParams(data: UpdateCustomerParams) {
        this._apiParams = data;
    }

    buildApiPayload(data: UpdateCustomerPayload) {
        this._apiPayload = data;
    }

    /**
     * Updates customer data using POST method
     * @returns Observable<any> - The response from the API
     */
    load(): Observable<any> {
        const url = this.getApiUrl();
        return this._http.post<any>(url, this._apiPayload);
    }
}
