/**
 * @fileoverview Waiver Routing Service
 * @description This service handles the routing logic for determining the appropriate
 *              page to navigate to based on waiver data. It centralizes the decision
 *              making process for both login and registration flows.
 * <AUTHOR> G
 * @version 1.0.0
 * @created 2025-08-15
 */

import { Injectable, inject, signal } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, finalize, map, switchMap, tap } from 'rxjs';
import { WaiverDetailsServiceBL } from './waiver-details-bl.service';
import {
    WaiverSetContainerDTO,
    WaiverSetResponse,
} from '../../interface/waiver.interface';

/**
 * WaiverRoutingService - Centralized Routing Decision Service
 *
 * This service determines the appropriate route based on waiver data:
 * - If single waiver: Navigate directly to detailed waiver page
 * - If multiple waivers: Navigate to list page for user selection
 *
 * Used in both login and registration flows to ensure consistent routing behavior.
 */
@Injectable()
export class WaiverRoutingServiceBL {
    private readonly _waiverDetailsBL = inject(WaiverDetailsServiceBL);
    private readonly _router = inject(Router);
    readonly loading = signal(false);
    /**
     * Determines and navigates to the appropriate page based on waiver data
     *
     * This method fetches waiver data, analyzes it, and navigates to the correct
     * page without using router outlets or intermediate redirects.
     *
     * @returns Observable that completes when navigation is determined
     */
    determineAndNavigate(): Observable<void> {
        this.loading.set(true);
        return this._waiverDetailsBL.waiverInfo$.pipe(
            switchMap(() => this._waiverDetailsBL.getWaiverSet()),
            map((response: WaiverSetResponse) =>
                this._waiverDetailsBL.getAllWaiverSetsByWaiverSetId(response)
            ),
            map((waiverSets: WaiverSetContainerDTO[]) =>
                this._waiverDetailsBL.filterWaiverSetsByEffectiveDates(
                    waiverSets
                )
            ),
            tap((waiverSets: WaiverSetContainerDTO[]) => {
                this.navigateBasedOnWaiverCount(waiverSets);
            }),
            map(() => {
                return void 0;
                // Return void to complete the observable
            }),
            finalize(() => {
                this.loading.set(false);
            })
            
        );
    }

    /**
     * Navigates to the appropriate page based on waiver count
     *
     * @param waiverSets - Array of available waiver sets
     */
    private navigateBasedOnWaiverCount(
        waiverSets: WaiverSetContainerDTO[]
    ): void {
        if (waiverSets.length === 0) {
            // No waivers available - handle error case
            this._router.navigate(['/my-signed-waivers']);
        } else if (waiverSets.length === 1) {
            // Single waiver - navigate directly to detailed page
            const waiverSet = waiverSets[0];
            this._router.navigate([
                '/waivers/sign-waiver',
                waiverSet.WaiverSetId,
            ]);
        } else {
            // Multiple waivers - navigate to list page
            this._router.navigate(['/waivers/list']);
        }
    }

    /**
     * Handles routing after successful login
     *
     * This method should be called after successful authentication
     * to determine the appropriate page to navigate to.
     */
    handlePostLoginRouting(): void {
        this.determineAndNavigate().subscribe();
    }

    /**
     * Handles routing after successful registration
     *
     * This method should be called after successful registration
     * to determine the appropriate page to navigate to.
     */
    handlePostRegistrationRouting(): void {
        this.determineAndNavigate().subscribe();
    }
}
