<app-client-header></app-client-header>
<header class="sticky top-0 w-full z-50 lg:z-40">
    <div class="w-full flex justify-between items-center px-6 lg:px-10 py-4 bg-surface-white drop-shadow-sm">
        <div class="flex items-center flex-grow gap-4 md:gap-10">
            <button class="lg:hidden text-primary min-h-6 min-w-6"
                (click)="toggleMobileSidebar(); $event.stopPropagation()" aria-label="Toggle menu">
                @if (!mobileSidebarOpen) {
                <img src="assets/icons/hamburger.svg" alt="open menu" />
                } @else {
                <img src="/assets/icons/menu-close.svg" alt="close menu" />
                }
            </button>

            <a routerLink="/" class="flex items-center">
                <img src="assets/icons/semnox-logo.svg" alt="Semnox Logo" class="min-w-[101px] hidden md:block" />
                <span class="block md:hidden text-primary text-2xl">Waivers</span>
            </a>

            @if(!siteContextService.selectedLocation?.IsMasterSite) {
            <div class="hidden lg:flex items-center gap-2">
                <img src="/assets/icons/location.svg" alt="Location" class="w-4 h-4" />
                <a routerLink="/site-selection" class="text-xs xl:text-sm">{{
                    siteContextService.selectedLocation?.SiteName
                    }}</a>
                <img src="/assets/icons/chevron-down.svg " alt="Dropdown" class="w-4 h-4" />
            </div>
            }

            <!-- Search Bar -->
            <div class="hidden lg:flex flex-grow relative mx-4 max-w-[568px] w-full">
                <lib-text-input placeholder="Search" [icon]="'/assets/icons/magnifier.svg'" [iconAlt]="'Magnifier'"
                    class="w-full"></lib-text-input>
            </div>
        </div>

        <div class="flex items-center justify-center space-x-6">
            <div class="flex items-center gap-4 md:gap-10">
                <!-- Shopping Cart -->
                <a href="#" class="text-primary flex items-center gap-2">
                    <img src="/assets/icons/cart.svg" alt="Cart" class="w-6 h-6" />
                    <span class="hidden md:block text-sm ">My Cart</span>
                </a>

                <!-- Language Selector -->
                <app-language-selection class="hidden lg:block"></app-language-selection>

                <app-faq></app-faq>
            </div>
        </div>
    </div>

    <div class="w-full bg-blue-100 flex justify-between items-center px-6 md:px-10">
        <div class="hidden lg:flex justify-center items-center">
            <div class="flex items-center justify-center space-x-6">
                <nav class="hidden lg:flex items-center justify-between space-x-6">
                    @for (item of navItems; track $index) {
                    <div class="flex flex-col items-center justify-between">
                        <a [routerLink]="item.link" routerLinkActive="text-secondary-blue font-medium"
                            [routerLinkActiveOptions]="{ exact: true }"
                            class="flex items-center gap-2 text-xs xl:text-sm text-primary hover:text-secondary-blue py-3">
                            <img [src]="item.icon" [alt]="item.label" class="w-4 h-4 xl:w-5 xl:h-5" />
                            <span> {{ item.label }}</span>
                        </a>
                        @if(router.url === item.link) {
                        <div class="h-1 w-[calc(100%+1px)] bg-secondary-blue rounded-t-lg"></div>
                        }
                    </div>
                    }

                    <!-- My Account -->
                    <app-dropdown-menu [items]="accountMenuItems" [alignRight]="true">
                        <button triggerButton
                            class="flex items-center text-xs xl:text-sm text-primary hover:text-secondary-blue">
                            <img src="/assets/icons/account.svg" alt="Account" class="w-4 h-4 xl:w-5 xl:h-5 mr-2" />
                            <span i18n="header.default.my-account" class="text-nowrap">My Account</span>
                            <img src="/assets/icons/chevron-down.svg" alt="Dropdown"
                                class="w-4 h-4 xl:w-5 xl:h-5 ml-2 transition-transform" />
                        </button>
                    </app-dropdown-menu>
                </nav>
            </div>
        </div>

        <button class="hidden lg:flex items-center gap-2" (click)="authService.logout()">
            <img src="/assets/icons/logout.svg" alt="Logout" class="w-4 h-4" />
            <span>Logout</span>
        </button>

        @if(!siteContextService.selectedLocation?.IsMasterSite) {
        <div class="lg:hidden flex items-center gap-2 py-3">
            <img src="/assets/icons/location.svg" alt="Location" class="w-4 h-4" />
            <a routerLink="/site-selection" class="text-xs xl:text-sm">{{
                siteContextService.selectedLocation?.SiteName
                }}</a>
            <img src="/assets/icons/chevron-down.svg " alt="Dropdown" class="w-4 h-4" />
        </div>
    }
        <app-language-selection class="lg:hidden py-3 ml-auto"></app-language-selection>
    </div>
</header>

<!-- Mobile Sidebar -->

<app-mobile-sidebar [isOpen]="mobileSidebarOpen" (closeSidebar)="closeSideBar()"></app-mobile-sidebar>