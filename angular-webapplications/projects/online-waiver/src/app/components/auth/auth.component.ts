/**
 * @fileoverview Auth component
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-17
 */

import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import {
    Router,
    RouterModule
} from '@angular/router';
import { TransactionDetailsDL, TransactionTimeDL } from 'lib-app-core';
import { TabItem, TabsComponent } from 'lib-ui-kit';
import { CookieService } from 'lib/lib-app-core/src/lib/services/cookie-service/cookie.service';
import { CreateDefaultCustomerDL } from '../../services/data-layer/create-default-customer-dl.service';
import { GetHtmlWaiverDL } from '../../services/data-layer/get-html-waiver-dl.service';
import { WaiverSetDL } from '../../services/data-layer/waiver-set-dl.service';
import { PreviewWaiverCardComponent } from '../preview-waiver-card/preview-waiver-card.component';

@Component({
    selector: 'app-auth',
    imports: [
        CommonModule,
        RouterModule,
        PreviewWaiverCardComponent,
        TabsComponent,
    ],
    templateUrl: './auth.component.html',
    styleUrl: './auth.component.scss',
    providers: [
        CreateDefaultCustomerDL,
        TransactionDetailsDL,
        WaiverSetDL,
        TransactionTimeDL,
        GetHtmlWaiverDL,
        CookieService,
    ],
})
export class AuthComponent {
    protected readonly router = inject(Router);

    protected readonly authTabs: TabItem[] = [
        {
            id: 'register',
            label: 'Register',
            subtitle: 'New customer?',
            route: '/auth/register',
            routerLinkActiveClasses:  "bg-surface-white text-secondary-blue -mb-[0.25rem] rounded-tl-[2rem] rounded-tr-[1.5rem] shadow-md after:content-[''] after:absolute after:h-full after:w-8 after:bg-transparent after:bottom-[4px] after:right-[-2rem] after:rounded-bl-[1.5rem] after:transition-all after:duration-150 after:shadow-curve-after"
        } ,
        {
            id: 'login',
            label: 'Login',
            subtitle: 'Existing customer?',
            route: '/auth/login',
            routerLinkActiveClasses: "bg-surface-white text-secondary-blue -mb-[0.25rem] rounded-tr-[2rem] rounded-tl-[1.5rem] shadow-md before:content-[''] before:absolute before:h-full before:w-8 before:bg-transparent before:bottom-[4px] before:left-[-2rem] before:rounded-br-[1.5rem] before:transition-all before:duration-150 before:shadow-curve-before"
        },
    ];

    protected readonly activeTabId = signal<string>('');

    ngOnInit() {
        // Set active tab based on current route
        const currentRoute = this.router.url;
        if (currentRoute.includes('/auth/register')) {
            this.activeTabId.set('register');
        } else if (currentRoute.includes('/auth/login')) {
            this.activeTabId.set('login');
        }
    }

    onTabChange(tabId: string) {
        this.activeTabId.set(tabId);

    }
}
