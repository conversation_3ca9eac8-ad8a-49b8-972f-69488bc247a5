/**
 * @fileoverview SignWaiverComponent handles the detailed view of a specific waiver
 * @description This component displays waiver details, allows users to preview waivers,
 * and manages the waiver signing process. It handles navigation, data loading,
 * and user interactions for the detailed waiver page.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-10
 */
import { CommonModule } from '@angular/common';
import {
    Component,
    inject,
    OnDestroy,
    OnInit,
    signal
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { map, switchMap, tap } from 'rxjs';
import {
    WaiverPreviewData
} from '../../interface/waiver.interface';
import { WaiverDetailsServiceBL } from '../../services/business-layer/waiver-details-bl.service';
import { ParticipantCardComponent } from '../participant-card/participant-card.component';
import { PreviewWaiverComponent } from '../preview-waiver/preview-waiver.component';
// import { WaiverSignatureComponent } from '../waiver-signature/waiver-signature.component';
import { ButtonComponent } from 'lib-ui-kit';
import { Subscription } from 'rxjs';
import { WaiverRoutingServiceBL } from '../../services/business-layer/waiver-routing-bl.service';
import { BreadCrumbService } from '../../shared/services/bread-crumb.service';
import { LoginEncryptionService } from 'lib/lib-auth/src/lib/services/login-encryption.service';
import { LibUserDetailDL } from 'lib/lib-auth/src/lib/data-layer/lib-auth-user-details-dl.service';

/**
 * Component for displaying detailed waiver information and managing waiver interactions
 *
 * This component provides:
 * - Waiver preview functionality
 * - Participant information display
 * - Navigation between waiver sections
 * - Breadcrumb management
 * - Waiver data loading and state management
 */
@Component({
    selector: 'app-sign-waiver',
    standalone: true,
    imports: [
        CommonModule,
        PreviewWaiverComponent,
        ParticipantCardComponent,
        ButtonComponent,
        // WaiverSignatureComponent,
        // ModalComponent,
    ],
    providers: [WaiverRoutingServiceBL,LoginEncryptionService, LibUserDetailDL ],
    templateUrl: './sign-waiver.component.html',
    styleUrl: './sign-waiver.component.scss',
})
export class SignWaiverComponent implements OnInit, OnDestroy {
    readonly waiverData = signal<WaiverPreviewData | null>(null);
    readonly waiverName = signal<string>('');
    readonly showSignatureModal = signal<boolean>(false);
    readonly signatureData = signal<string>('');
    readonly selectedWaiverIndex = signal<number>(-1);
    readonly isWaiverSigned = signal<boolean>(false);
    readonly router = inject(Router);
    readonly route = inject(ActivatedRoute);
    private readonly _waiverDetailsBL = inject(WaiverDetailsServiceBL);
    private readonly breadcrumbService = inject(BreadCrumbService);
    private subscription?: Subscription;

    ngOnInit(): void {
        this.loadWaiverData();
    }

    ngOnDestroy(): void {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }

    /**
     * Shows the signature modal for a specific waiver
     *
     * @param waiverIndex - The index of the waiver to sign
     */
    showSignModal(waiverIndex: number): void {
        this.selectedWaiverIndex.set(waiverIndex);
        this.showSignatureModal.set(true);
    }

    /**
     * Closes the signature modal and resets the selected waiver index
     */
    closeSignatureModal(): void {
        this.showSignatureModal.set(false);
        this.selectedWaiverIndex.set(-1);
    }

    /**
     * Handles the signature submission event
     *
     * @param signatureBase64 - The signature data in base64 format
     */
    onSignatureSubmitted(signatureBase64: string): void {
        this.signatureData.set(signatureBase64);
        // Mark the waiver as signed
        this.isWaiverSigned.set(true);
        this.showSignatureModal.set(false);
        this.selectedWaiverIndex.set(-1);
    }

    /**
     * Gets the name of the currently selected waiver
     *
     * @returns The waiver name or a default value if not available
     */
    getSelectedWaiverName(): string {
        return this.waiverName() || 'Sign Waiver';
    }

    /**
     * Navigates to the next step in the waiver process
     * Currently navigates to the waiver list page
     */
    proceedToNext(): void {
        // Navigate to the next step or page
        this.router.navigate(['/waivers/check-in']);
    }

    /**
     * Loads waiver data based on the route parameters
     *
     * This method:
     * 1. Extracts the waiverSetId from route parameters
     * 2. Fetches waiver information from the business layer
     * 3. Filters and processes the waiver data
     * 4. Updates component state with the processed data
     * 5. Manages breadcrumb navigation
     */
    loadWaiverData(): void {
        // Unsubscribe from previous subscription if exists
        if (this.subscription) {
            this.subscription.unsubscribe();
        }

        this.subscription = this.route.params
            .pipe(
                // Extract waiverSetId from route parameters
                map((params) => params['waiverSetId']),

                // Fetch and process waiver data
                switchMap((waiverSetId) => {
                    return this._waiverDetailsBL.waiverInfo$.pipe(
                        switchMap(() => this._waiverDetailsBL.getWaiverSet()),
                        map((response) =>
                            this._waiverDetailsBL.getAllWaiverSetsByWaiverSetId(
                                response
                            )
                        ),
                        map((waiverSets) =>
                            this._waiverDetailsBL.filterWaiverSetsByEffectiveDates(
                                waiverSets
                            )
                        ),
                        map((waiverSets) =>
                            waiverSets.find(
                                (ws) =>
                                    ws.WaiverSetId.toString() === waiverSetId
                            )
                        )
                    );
                }),

                // Update component state and navigation
                tap((waiverSet) => {
                    if (waiverSet) {
                        // Set waiver name
                        this.waiverName.set(waiverSet.Name);

                        // Update breadcrumb with dynamic waiver name
                        this.route.snapshot.data['breadcrumb'] = waiverSet.Name;

                        // Refresh breadcrumb service
                        this.breadcrumbService.refreshBreadcrumbs(this.route);

                        // Load the first waiver for this waiver set
                        this._waiverDetailsBL.getWaiverDataWithState(
                            waiverSet,
                            (waiverData) => {
                                this.waiverData.set(waiverData);
                            }
                        );
                    }
                })
            )
            .subscribe();
    }
}
